const body = document.querySelector('body');

const countriesSelect = document.querySelector('#countries');
countriesSelect.addEventListener('change', updateCitiesList);

const citiesSelect = document.querySelector('#cities');
citiesSelect.addEventListener('change', onCitySelect);

const compass = document.querySelector('#compass');

const countries = locations.map(location => location.name);
const cities = [];

function getDistance (point1, point2) {
  const lt1 = toRadians(point1.latitude);
  const ln1 = toRadians(point1.longitude);
  const lt2 = toRadians(point2.latitude);
  const ln2 = toRadians(point2.longitude);

  //earth radius in kilometers
  const radius = 6371;

  //result in Kilometers
  return Math.acos(Math.sin(lt2) * Math.sin(lt1) + Math.cos(lt2) * Math.cos(lt1) * Math.cos(ln2 - ln1)) * radius;
};

const storeLocation = city => chrome.storage.sync.set({ city }, () => null);

function retrieveLocation() {
  chrome.storage.sync.get(['city'], function(items) {
    if (items.city) {
      const { country, id, latitude, longitude, name } = items.city;

      // Update UI with stored city
      countriesSelect.value = country;
      updateCitiesList({
        currentTarget: {
          value: country,
        },
      });
      citiesSelect.value = id;

      // Update the compass and other UI elements
      updateChosenCity({ country, id, latitude, longitude, name });
    } else {
      // If no city is stored, just hide the compass
      hideCompass();
    }
  });
}

function findCity({ latitude, longitude }) {
  let closestCity = cities[0];
  let closestDistance = Number.POSITIVE_INFINITY;

  cities.forEach(city => {
    const distance = getDistance(city, { latitude, longitude });
    if (distance < closestDistance) {
      closestCity = city;
      closestDistance = distance;
    }
  });

  return closestDistance < 100 ? closestCity : null;
};

const toRadians = (deg) => deg * Math.PI / 180;
const toDegrees = (rad) => rad * 180 / Math.PI;

function bearing({ latitude, longitude }) {
  const startLat = toRadians(latitude);
  const startLng = toRadians(longitude);

  // MECCA
  const destLat = toRadians(21.3891);
  const destLng = toRadians(39.8579);

  const y = Math.sin(destLng - startLng) * Math.cos(destLat);
  const x = Math.cos(startLat) * Math.sin(destLat) - Math.sin(startLat) * Math.cos(destLat) * Math.cos(destLng - startLng);
  const brng = toDegrees(Math.atan2(y, x));

  // return (brng + 360) % 360;
  return brng + 180;
}

function inflateCities() {
  locations.forEach(location => {
    cities.push(...location.cities.map(city => ({
      country: location.name,
      ...city,
    })));
  });
}

function plotCityName(city) {
  const cityBox = document.querySelector('#city-box');
  cityBox.innerText = `${city.name} ${city.country}`;
}

function plotCoordinates({ latitude, longitude }) {
  const coordsDiv = document.createElement('h1');
  coordsDiv.innerText = `${latitude} ${longitude}`;
  coordsDiv.style.textAlign = 'center';

  body.appendChild(coordsDiv);
}

function plotOrientation({ latitude, longitude }) {
  const brng = bearing({ latitude, longitude });

  const hand = document.createElement('div');
  hand.classList.add('hand');
  hand.style.transform = `translate(100px, 100px) rotate(${brng + 90 - 11.5}deg)`;

  compass.innerHTML = '';
  compass.appendChild(hand);

  const degrees = Math.floor(brng);
  const minutes = Math.floor(60 * (brng - degrees));

  const degreesElement = document.querySelector('#bearing #degrees');
  degreesElement.innerText = degrees;

  const minutesElement = document.querySelector('#bearing #minutes');
  minutesElement.innerText = minutes;
}

function navigateToGoogleMap() {
  chrome.tabs.create({ url: 'https://www.google.com/maps' });
}

async function getCurrentTab() {
  const queryOptions = { active: true, currentWindow: true };
  const [tab] = await chrome.tabs.query(queryOptions);
  return tab;
}

const isGoogleMaps = (tab) => tab?.url?.indexOf?.('https://www.google.com/maps/') === 0;


function inflateMenu() {
  locations.forEach(({ name}) => {
    const option = document.createElement('option');
    option.value = name;
    option.innerText = name;
    countriesSelect.appendChild(option);
  });
}

function updateCitiesList({ currentTarget }) {
  citiesSelect.innerHTML = '<option value="">Select a city</option>';

  locations.find(({ name }) => name === currentTarget.value).cities.forEach(city => {
    const option = document.createElement('option');
    option.value = city.id;
    option.innerText = city.name;
    citiesSelect.appendChild(option);
  });
}

function updateChosenCity(city) {
  plotCityName(city);
  plotOrientation(city);
  showCompass();

  storeLocation(city);
}

function onCitySelect(event) {
  const cityId = event.currentTarget.value;
  if (cityId) {
    const city = cities.find(city => city.id == cityId);
    if (city) {
      updateChosenCity(city);
    }
  } else {
    // If no city is selected, hide the compass
    hideCompass();
  }
}

function hideCompass() {
  compass.style.visibility = 'collapse';
  compass.style.height = 0;
}

function showCompass() {
  compass.style.visibility = null;
  compass.style.height = null;
}

async function checkGoogleMaps() {
  try {
    const currentTab = await getCurrentTab();

    if (isGoogleMaps(currentTab)) {
      // Only proceed if we have a valid Google Maps URL with coordinates
      if (currentTab.url.includes('@') && currentTab.url.split('@').length > 1) {
        const [latitude, longitude] = currentTab.url.split('@')[1].split(',');

        if (latitude && longitude) {
          const city = findCity({ latitude: parseFloat(latitude), longitude: parseFloat(longitude) });

          plotOrientation({ latitude: parseFloat(latitude), longitude: parseFloat(longitude) });

          if (city) {
            plotCityName(city);
            countriesSelect.value = city.country;
            updateCitiesList({
              currentTarget: {
                value: city.country,
              },
            });
            citiesSelect.value = city.id;

            // Store this city for future use
            storeLocation(city);
          } else {
            plotCoordinates({ latitude, longitude });
          }

          // Show the compass since we have coordinates
          showCompass();
          return;
        }
      }
    }

    // If we're not on Google Maps or couldn't extract coordinates, show the hint
    const hint = document.querySelector('#google-maps-hint');
    hint.style.visibility = 'visible';

    const link = hint.querySelector('#google-maps-link');
    link.addEventListener('click', navigateToGoogleMap);
  } catch (error) {
    console.error('Error checking Google Maps:', error);
    // If there's an error, don't show the Google Maps hint
  }
}

document.addEventListener('DOMContentLoaded', function() {
  // Hide compass initially
  hideCompass();

  // Initialize data
  inflateCities();
  inflateMenu();

  // Try to retrieve stored location first
  retrieveLocation();

  // Then check if we're on Google Maps
  checkGoogleMaps();
});
