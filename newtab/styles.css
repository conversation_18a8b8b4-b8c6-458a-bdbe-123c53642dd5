/* Tavoos New Tab Styles */
:root {
  /* Tavoos Brand Colors */
  --tavoos-primary: rgb(0, 121, 107);
  --tavoos-dark: rgb(0, 90, 80);
  --tavoos-darker: rgb(0, 70, 60);
  --tavoos-light: rgb(0, 200, 180);
  --tavoos-orange: rgb(255, 144, 0);
  --tavoos-orange-dark: rgb(230, 130, 0);

  /* UI Colors */
  --text-primary: white;
  --text-secondary: rgba(255, 255, 255, 0.8);
  --bg-overlay: rgba(255, 255, 255, 0.1);
  --border-light: rgba(255, 255, 255, 0.2);
  --border-medium: rgba(255, 255, 255, 0.3);
  --highlight-bg: rgba(255, 255, 255, 0.25);

  /* Button Colors */
  --btn-bg: var(--tavoos-orange);
  --btn-text: black;
  --btn-hover-bg: var(--tavoos-orange-dark);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Font declarations */
@font-face {
  font-family: 'Me Quran';
  src: url('../fonts/me_quran.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--tavoos-primary);
  color: var(--text-primary);
  line-height: 1.6;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header Styles */
header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-medium);
}

.logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  padding: 5px 10px;
  border-radius: 8px;
  transition: background-color 0.3s;
}

.logo:hover {
  background-color: var(--bg-overlay);
}

.logo img {
  width: 40px;
  height: 40px;
  margin-right: 10px;
  border-radius: 8px;
}

.logo h1 {
  font-size: 24px;
  color: var(--text-primary);
}

.date-time {
  text-align: right;
}

#current-date, #current-time {
  font-size: 16px;
  color: var(--text-secondary);
}

#current-time {
  font-size: 24px;
  font-weight: bold;
  color: var(--text-primary);
}

/* Main Content Styles */
main {
  flex: 1;
}

.row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.card {
  background-color: var(--bg-overlay);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid var(--border-light);
}

h2 {
  color: var(--text-primary);
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-medium);
}

/* Prayer Times Section */
.prayer-times {
  flex: 1;
  max-width:fit-content;
}

.location-selector {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.location-selector select {
  flex: 1;
  padding: 8px;
  border: 1px solid var(--border-medium);
  border-radius: 4px;
  background-color: var(--bg-overlay);
  color: var(--text-primary);
}

#location-display {
  font-size: 16px;
  margin-bottom: 15px;
  color: var(--text-primary);
  font-weight: bold;
}

.prayer-schedule {
  display: grid;
  gap: 15px;
}

.prayer-item {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  background-color: var(--bg-overlay);
  border-radius: 4px;
  border-left: 4px solid var(--text-primary);
}

.prayer-name {
  font-weight: bold;
  color: var(--text-primary);
}

.prayer-time {
  color: var(--text-secondary);
}

.active-prayer {
  background-color: var(--tavoos-orange);
  border-left: 4px solid var(--tavoos-light);
  font-weight: bold;

  .prayer-time, .prayer-name {
    color: black;
  }
}

/* Qibla Direction Section */
.qibla-direction {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  overflow: hidden;
}

/* Add a map background to the entire qibla direction section */
.qibla-direction::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  opacity: 0.15;
  pointer-events: none;
}

.compass {
  width: 250px;
  height: 250px;
  background-image: url('../qibla/images/compass_rose.png');
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  margin: 20px 0;
  position: relative;
  transition: all 0.3s ease;
}

.compass:hover {
  transform: scale(1.05);
}

.hand {
  width: 70px;
  border: 2px solid black;
  background-color: black;
  position: absolute;
  transform-origin: center left;
  z-index: 10;
}

.hand::after {
  content: '';
  width: 10px;
  border: 1em solid transparent;
  border-left: 1em solid black;
  position: absolute;
  left: 100%;
  top: -1em;
  z-index: 10;
}

/* Location marker is no longer used */

/* Map styling is now handled in JavaScript */

.bearing {
  font-size: 18px;
  font-weight: bold;
  color: var(--text-primary);
}

/* Quran Verse Section */
.quran-verse {
  text-align: center;
}

.arabic-text {
  font-size: 28px;
  line-height: 2;
  margin: 20px 0;
  direction: rtl;
  font-family: 'Me Quran', 'Traditional Arabic', 'Scheherazade', serif;
  color: var(--text-primary);
  text-align: center;
}

#verse-container {
  padding: 15px;
  background-color: var(--bg-overlay);
  border-radius: 8px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  margin: 15px 0;
  border: 1px solid var(--border-light);
}

#verse-translation {
  font-size: 16px;
  line-height: 1.6;
  margin: 15px 0;
  color: #5a6268;
  padding: 0 15px;
}

.verse-reference {
  font-style: italic;
  color: var(--text-secondary);
  margin-bottom: 15px;
}

.bismillah {
  font-family: 'Me Quran', 'Traditional Arabic', 'Scheherazade', serif;
  font-size: 24px;
  text-align: center;
  margin-bottom: 15px;
  color: var(--tavoos-light);
  direction: rtl;
}

.verse-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 15px;
}

#new-verse-btn {
  background-color: var(--btn-bg);
  color: var(--btn-text);
  border: 1px solid black;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
  font-weight: bold;
}

#new-verse-btn:hover {
  background-color: white;
}

.audio-container {
  margin-top: 15px;
  display: flex;
  justify-content: center;
  position: relative;
}

audio {
  width: 100%;
  max-width: 400px;
  border-radius: 30px;
  background-color: transparent;
  margin: 10px 0;
  border: 2px solid black;
}

/* Aggressive styling to remove gray background */
audio::-webkit-media-controls,
audio::-webkit-media-controls-enclosure,
audio::-webkit-media-controls-panel {
  border-radius: 30px;
  background-color: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
}

/* Target Chrome specifically */
@media screen and (-webkit-min-device-pixel-ratio:0) {
  audio {
    background: transparent !important;
  }
}

/* Make time display text black for better visibility and remove glow */
audio::-webkit-media-controls-current-time-display,
audio::-webkit-media-controls-time-remaining-display {
  color: black !important;
  text-shadow: none !important;
  background: transparent !important;
  font-weight: bold;
}

.verse-details-btn {
  display: inline-block;
  background-color: transparent;
  color: var(--text-primary);
  border: 1px solid var(--text-primary);
  padding: 7px 16px;
  border-radius: 4px;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.3s;
  font-weight: bold;
}

.verse-details-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: var(--text-primary);
  color: var(--text-primary);
}

/* Footer Styles */
footer {
  text-align: center;
  margin-top: 30px;
  padding-top: 15px;
  border-top: 1px solid var(--border-medium);
  color: var(--text-secondary);
}

/* Settings Button and Modal */
.settings-container {
  margin-bottom: 15px;
}

.settings-btn {
  background-color: var(--btn-bg);
  color: var(--btn-text);
  border: 1px solid black;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
  font-weight: bold;
}

.settings-btn:hover {
  background-color: white;
}

.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
  background-color: var(--tavoos-primary);
  margin: 10% auto;
  padding: 20px;
  border: 1px solid var(--border-medium);
  border-radius: 8px;
  width: 80%;
  max-width: 500px;
  color: var(--text-primary);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  position: relative;
}

.modal-content h2 {
  margin-top: 10px;
  padding-right: 30px;
}

.close {
  color: var(--text-secondary);
  float: right;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
  line-height: 1;
  margin-top: -5px;
  position: relative;
}

.close:hover {
  color: var(--text-primary);
}

/* Settings Groups */
.settings-group {
  display: flex;
  align-items: center;
  margin: 15px 0;
  gap: 10px;
}

/* Toggle Switch */
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
}

input:checked + .slider {
  background-color: var(--tavoos-light);
}

input:focus + .slider {
  box-shadow: 0 0 1px var(--tavoos-light);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

.slider.round {
  border-radius: 24px;
}

.slider.round:before {
  border-radius: 50%;
}

/* Prayer Checkboxes */
.prayer-checkboxes {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 15px;
  margin: 15px 0;
}

.prayer-checkbox {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Save Button */
.save-btn {
  background-color: var(--btn-bg);
  color: var(--btn-text);
  border: 1px solid black;
  padding: 10px 25px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  margin-top: 15px;
  transition: background-color 0.3s;
  display: block;
  width: auto;
  margin-left: auto;
  margin-right: 0;
  font-weight: bold;
}

.save-btn:hover {
  background-color: white;
}

/* Minutes Before Select */
#minutes-before {
  background-color: var(--bg-overlay);
  color: var(--text-primary);
  border: 1px solid var(--border-medium);
  padding: 5px;
  border-radius: 4px;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .row {
    flex-direction: column;
  }
}
