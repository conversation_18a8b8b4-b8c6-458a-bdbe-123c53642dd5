#!/bin/bash

# Tavoos Chrome Extension Packing Script
# This script packs the extension into a .zip file for submission to the Chrome Web Store

# Get the current version from manifest.json
VERSION=$(grep -o '"version": "[^"]*"' manifest.json | cut -d'"' -f4)
echo "Packing Tavoos Extension version $VERSION"

# Create a zip file with all necessary files
# Exclude development files, git files, and the script itself
zip -r "tavoos-extension-v$VERSION.zip" . -x "*.git*" "*.DS_Store" "pack.sh" "*.zip" "*.pem" "*.crx"

echo "✅ Extension packed successfully as tavoos-extension-v$VERSION.zip"
echo "You can now upload this file to the Chrome Web Store Developer Dashboard."
