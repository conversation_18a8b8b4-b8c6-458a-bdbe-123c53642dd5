/** @type {import('tailwindcss').Config} */
export default {
	content: ['./src/**/*.{html,js,svelte,ts}'],
	theme: {
		extend: {
			colors: {
				'tavoos-primary': 'rgb(0, 121, 107)',
				'tavoos-dark': 'rgb(0, 90, 80)',
				'tavoos-darker': 'rgb(0, 70, 60)',
				'tavoos-light': 'rgb(0, 200, 180)',
				'tavoos-orange': 'rgb(255, 144, 0)',
				'tavoos-orange-dark': 'rgb(230, 130, 0)',
				'white-10': 'rgba(255, 255, 255, 0.1)',
				'white-5': 'rgba(255, 255, 255, 0.05)',
			},
			fontFamily: {
				'quran': ['Me Quran', 'Traditional Arabic', 'Scheherazade', 'serif'],
			},
			backdropBlur: {
				xs: '2px',
			}
		},
	},
	plugins: [],
};
