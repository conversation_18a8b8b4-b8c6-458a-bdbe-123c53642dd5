<script lang="ts">
  import { onMount } from 'svelte';
  import {
    prayerTimes,
    formattedPrayerTimes,
    nextPrayer,
    isLoadingPrayerTimes,
    prayerTimesError,
    prayerTimesActions
  } from '../stores/prayerTimesStore.js';
  import { currentLocation, locationActions } from '../stores/locationStore.js';
  import LocationSelector from './LocationSelector.svelte';
  import NotificationSettings from './NotificationSettings.svelte';

  let showLocationSelector = false;
  let showNotificationSettings = false;

  // Subscribe to location changes and update prayer times
  $: if ($currentLocation?.city) {
    updatePrayerTimes();
  }

  async function updatePrayerTimes() {
    if ($currentLocation?.city) {
      await prayerTimesActions.loadPrayerTimes(
        $currentLocation.city.latitude,
        $currentLocation.city.longitude
      );
    }
  }

  async function handleLocationChange() {
    if ($currentLocation) {
      await locationActions.saveCurrentLocation();
      await updatePrayerTimes();
    }
    showLocationSelector = false;
  }

  function toggleLocationSelector() {
    showLocationSelector = !showLocationSelector;
  }

  function toggleNotificationSettings() {
    showNotificationSettings = !showNotificationSettings;
  }

  onMount(async () => {
    await updatePrayerTimes();
  });
</script>

<div class="card rounded-lg p-6">
  <!-- Header -->
  <div class="flex justify-between items-center mb-4">
    <h2 class="text-xl font-semibold border-b border-white/30 pb-2">Prayer Times</h2>
    <div class="flex space-x-2">
      <button
        class="btn-secondary px-3 py-1 text-sm rounded"
        on:click={toggleNotificationSettings}
      >
        Settings
      </button>
      <button
        class="btn-secondary px-3 py-1 text-sm rounded"
        on:click={toggleLocationSelector}
      >
        Location
      </button>
    </div>
  </div>

  <!-- Location Display -->
  {#if $currentLocation}
    <div class="mb-4 text-center">
      <h3 class="text-lg font-medium">{$currentLocation.city.name}</h3>
      <p class="text-white/80 text-sm">{$currentLocation.country.name}</p>
    </div>
  {/if}

  <!-- Location Selector Modal -->
  {#if showLocationSelector}
    <div class="fixed inset-0 modal-backdrop flex items-center justify-center z-50">
      <div class="bg-tavoos-primary p-6 rounded-lg border border-white/20 max-w-md w-full mx-4">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold">Select Location</h3>
          <button
            class="text-white/60 hover:text-white text-xl"
            on:click={() => showLocationSelector = false}
          >
            ×
          </button>
        </div>
        <LocationSelector on:locationChanged={handleLocationChange} />
        <div class="mt-4 flex justify-end">
          <button
            class="btn-primary px-4 py-2 rounded"
            on:click={() => showLocationSelector = false}
          >
            Close
          </button>
        </div>
      </div>
    </div>
  {/if}

  <!-- Notification Settings Modal -->
  {#if showNotificationSettings}
    <div class="fixed inset-0 modal-backdrop flex items-center justify-center z-50">
      <div class="bg-tavoos-primary p-6 rounded-lg border border-white/20 max-w-md w-full mx-4">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold">Notification Settings</h3>
          <button
            class="text-white/60 hover:text-white text-xl"
            on:click={() => showNotificationSettings = false}
          >
            ×
          </button>
        </div>
        <NotificationSettings />
        <div class="mt-4 flex justify-end">
          <button
            class="btn-primary px-4 py-2 rounded"
            on:click={() => showNotificationSettings = false}
          >
            Close
          </button>
        </div>
      </div>
    </div>
  {/if}

  <!-- Prayer Times List -->
  {#if $isLoadingPrayerTimes}
    <div class="text-center py-8">
      <p class="text-white/80">Loading prayer times...</p>
    </div>
  {:else if $prayerTimesError}
    <div class="text-center py-8">
      <p class="text-red-300">Error: {$prayerTimesError}</p>
      <button
        class="btn-primary px-4 py-2 rounded mt-2"
        on:click={updatePrayerTimes}
      >
        Retry
      </button>
    </div>
  {:else if $formattedPrayerTimes}
    <div class="space-y-3">
      {#each $formattedPrayerTimes as prayer}
        <div
          class="flex justify-between items-center p-3 rounded border-l-4 transition-colors"
          class:bg-tavoos-orange={prayer.isCurrent}
          class:border-tavoos-orange={prayer.isCurrent}
          class:text-black={prayer.isCurrent}
          class:bg-white-10={!prayer.isCurrent}
          class:border-white={!prayer.isCurrent}
        >
          <span class="font-medium">{prayer.displayName}</span>
          <span class="text-sm opacity-80">{prayer.formattedTime}</span>
        </div>
      {/each}
    </div>

    <!-- Next Prayer -->
    {#if $nextPrayer}
      <div class="mt-4 p-3 bg-white/5 rounded border border-white/20">
        <p class="text-sm text-white/80">Next Prayer:</p>
        <p class="font-medium">
          {$nextPrayer.displayName} at {$nextPrayer.time}
        </p>
      </div>
    {/if}
  {:else}
    <div class="text-center py-8">
      <p class="text-white/80">No prayer times available</p>
    </div>
  {/if}
</div>
