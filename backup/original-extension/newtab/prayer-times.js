// Prayer Times Service

class PrayerTimesService {
  constructor() {
    this.apiUrl = 'https://behnegar.app/api/v1/tavoos/pray-times-v2/coordinates';
  }

  // Fetch prayer times for a specific city
  async getPrayerTimesByCity(city, country) {
    // For this method, we need coordinates
    // Find the city in the locations data to get its coordinates
    const cityData = cities.find(c => c.name === city && c.country === country);

    if (cityData) {
      return this.getPrayerTimesByCoordinates(cityData.latitude, cityData.longitude);
    } else {
      console.error('City not found in locations data');
      return null;
    }
  }

  // Fetch prayer times based on coordinates
  async getPrayerTimesByCoordinates(latitude, longitude) {
    try {
      const url = `${this.apiUrl}/${latitude}/${longitude}/schedule?period=today`;
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error('Failed to fetch prayer times');
      }

      const data = await response.json();

      // Map the Tavoos API response to the format expected by the UI
      return {
        Fajr: data.fajr,
        Sunrise: data.sunrise,
        Dhuhr: data.dhohr,
        Asr: data.aser,
        Maghrib: data.maghreb,
        Isha: data.isha
      };
    } catch (error) {
      console.error('Error fetching prayer times by coordinates:', error);
      return null;
    }
  }

  // Format time from 24-hour to 12-hour format
  formatTime(time24) {
    const [hours, minutes] = time24?.split(':') ?? [0, 0];
    const hour = parseInt(hours, 10);
    const period = hour >= 12 ? 'PM' : 'AM';
    const hour12 = hour % 12 || 12;
    return `${hour12}:${minutes} ${period}`;
  }

  // Get user's current location
  async getCurrentLocation() {
    return new Promise((resolve, reject) => {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            resolve({
              latitude: position.coords.latitude,
              longitude: position.coords.longitude
            });
          },
          (error) => {
            console.error('Error getting location:', error);
            reject(error);
          }
        );
      } else {
        reject(new Error('Geolocation is not supported by this browser.'));
      }
    });
  }
}

// Export the service
const prayerTimesService = new PrayerTimesService();
