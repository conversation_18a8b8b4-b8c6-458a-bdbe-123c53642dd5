// Quran Verses Service

class QuranService {
  constructor() {
    this.apiUrl = 'https://behnegar.app/api/v1/tavoos';
    this.totalVerses = 6236; // Total number of verses in the Quran
    this.versesCache = [];
    this.currentVerseIndex = 0;
  }

  // Get a random verse from the Quran using Tavoos API
  async getRandomVerse() {
    try {
      // Generate a random verse ID between 1 and totalVerses (6236)
      const randomVerseId = Math.floor(Math.random() * this.totalVerses) + 1;

      // Call the Tavoos API to get a verse by ID
      const response = await fetch(`${this.apiUrl}/quran/verses/${randomVerseId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch verse from Tavoos API');
      }

      const data = await response.json();

      // Process the API response based on the actual structure
      return {
        arabic: data.verse.text, // The Arabic text of the verse
        translation: '-', // Simple placeholder for translation
        surahName: data.chapter.title, // Arabic name of the Surah
        surahEnglishName: data.chapter.englishTitle, // English name of the Surah
        verseNumber: data.verse.index, // Verse number within the Surah
        surahNumber: data.chapter.id, // Surah number
        verseId: data.verse.indexAll, // Global verse index (1-6236)
        recitationUrl: `https://file-keeper.com/media/audio/recitation/mohammad-seddigh-menshawi/morattal/${data.verse.indexAll}.mp3` // URL to the recitation audio
      };
    } catch (error) {
      console.error('Error fetching random verse:', error);
      return {
        arabic: 'خطأ في تحميل الآية',
        translation: '-',
        surahName: 'خطأ',
        surahEnglishName: 'Error',
        verseNumber: 0,
        surahNumber: 0,
        verseId: 0,
        recitationUrl: ''
      };
    }
  }

  // Preload a set of verses to avoid frequent API calls
  async preloadVerses(count = 5) {
    try {
      // Generate random verse IDs
      const verseIds = [];
      for (let i = 0; i < count; i++) {
        const randomId = Math.floor(Math.random() * this.totalVerses) + 1;
        verseIds.push(randomId);
      }

      // Fetch each verse individually
      const promises = verseIds.map(id =>
        fetch(`${this.apiUrl}/quran/verses/${id}`)
          .then(response => {
            if (!response.ok) {
              throw new Error(`Failed to fetch verse ID ${id}`);
            }
            return response.json();
          })
          .then(data => ({
            arabic: data.verse.text,
            translation: '-',
            surahName: data.chapter.title,
            surahEnglishName: data.chapter.englishTitle,
            verseNumber: data.verse.index,
            surahNumber: data.chapter.id,
            verseId: data.verse.indexAll,
            recitationUrl: `https://file-keeper.com/media/audio/recitation/mohammad-seddigh-menshawi/morattal/${data.verse.indexAll}.mp3`
          }))
          .catch(error => {
            console.error(`Error fetching verse ID ${id}:`, error);
            return null;
          })
      );

      // Wait for all requests to complete
      const results = await Promise.all(promises);

      // Filter out any null results from failed requests
      this.versesCache = results.filter(result => result !== null);
      this.currentVerseIndex = 0;
    } catch (error) {
      console.error('Error preloading verses:', error);
    }
  }

  // Get the next verse from cache or fetch a new one
  async getNextVerse() {
    // Always get a random verse instead of using the cache
    return await this.getRandomVerse();
  }

  // Alias for getRandomVerse for backward compatibility
  async getVerseOfTheDay() {
    return await this.getRandomVerse();
  }
}

// Export the service
const quranService = new QuranService();
