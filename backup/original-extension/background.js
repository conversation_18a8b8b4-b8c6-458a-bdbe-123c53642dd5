// Service worker for Tavoos Chrome Extension

// Prayer Times API URL
const PRAYER_TIMES_API_URL = 'https://behnegar.app/api/v1/tavoos/pray-times-v2/coordinates';

// Listen for installation
chrome.runtime.onInstalled.addListener(() => {
  console.log('Tavoos extension installed');

  // Initialize notification settings if not already set
  chrome.storage.sync.get(['notificationSettings'], (result) => {
    if (!result.notificationSettings) {
      // Default settings: notifications enabled for all prayers
      const defaultSettings = {
        enabled: true,
        prayers: {
          Fajr: true,      // Dawn
          Sunrise: false,  // Not typically considered a prayer time
          Dhuhr: true,     // Noon
          Asr: true,       // Afternoon
          Maghrib: true,   // Dusk
          Isha: true       // Dinner
        }
      };

      chrome.storage.sync.set({ notificationSettings: defaultSettings }, () => {
        console.log('Notification settings initialized');
      });
    }
  });

  // Set up daily alarm to fetch prayer times
  setupDailyAlarm();
});

// Set up a daily alarm to fetch prayer times at 2:00 AM
function setupDailyAlarm() {
  // Calculate time until next 2:00 AM
  const now = new Date();
  const scheduledTime = new Date(now);

  // Set time to 2:00:00 AM
  scheduledTime.setHours(2, 0, 0, 0);

  // If it's already past 2:00 AM, schedule for tomorrow
  if (now >= scheduledTime) {
    scheduledTime.setDate(scheduledTime.getDate() + 1);
  }

  // Calculate minutes until scheduled time
  const minutesUntil = (scheduledTime - now) / (60 * 1000);

  // Create the alarm
  chrome.alarms.create('fetchPrayerTimes', {
    delayInMinutes: minutesUntil,
    periodInMinutes: 24 * 60  // Repeat every 24 hours
  });

  console.log(`Prayer times will be fetched at 2:00 AM (in ${minutesUntil.toFixed(2)} minutes)`);

  // Also fetch immediately for initial setup
  fetchPrayerTimesAndScheduleNotifications();
}

// Listen for alarms
chrome.alarms.onAlarm.addListener((alarm) => {
  if (alarm.name === 'fetchPrayerTimes') {
    fetchPrayerTimesAndScheduleNotifications();
  } else if (alarm.name.startsWith('prayerNotification_')) {
    const prayerName = alarm.name.split('_')[1];
    sendPrayerNotification(prayerName);
  }
});

// Fetch prayer times and schedule notifications
async function fetchPrayerTimesAndScheduleNotifications() {
  // Check if notifications are enabled
  const { notificationSettings } = await chrome.storage.sync.get(['notificationSettings']);
  if (!notificationSettings || !notificationSettings.enabled) {
    console.log('Prayer notifications are disabled');
    return;
  }

  // Get the stored city
  const { city } = await chrome.storage.sync.get(['city']);
  if (!city) {
    console.log('No city selected, cannot fetch prayer times');
    return;
  }

  try {
    // Get coordinates from the city
    const { latitude, longitude } = city;

    // Fetch prayer times using Tavoos API
    const response = await fetch(
      `${PRAYER_TIMES_API_URL}/${latitude}/${longitude}/schedule?period=today`
    );

    if (!response.ok) {
      throw new Error('Failed to fetch prayer times');
    }

    const data = await response.json();

    // Map the prayer times to the format we need
    const timings = {
      Fajr: data.fajr,
      Sunrise: data.sunrise,
      Dhuhr: data.dhohr,
      Asr: data.aser,
      Maghrib: data.maghreb,
      Isha: data.isha
    };

    // Get today's date
    const today = new Date();
    const date = `${today.getDate()}-${today.getMonth() + 1}-${today.getFullYear()}`;

    // Store the prayer times
    chrome.storage.local.set({ prayerTimes: timings, prayerTimesDate: date }, () => {
      console.log('Prayer times stored for', date);
    });

    // Schedule notifications for each prayer
    schedulePrayerNotifications(timings, notificationSettings);
  } catch (error) {
    console.error('Error fetching prayer times:', error);
  }
}

// Schedule notifications for prayer times
function schedulePrayerNotifications(timings, settings) {
  // Get all existing alarms
  chrome.alarms.getAll((alarms) => {
    // Find the fetchPrayerTimes alarm to preserve its timing
    const fetchAlarm = alarms.find(a => a.name === 'fetchPrayerTimes');

    // Clear all prayer notification alarms but keep the fetchPrayerTimes alarm
    alarms.forEach(alarm => {
      if (alarm.name.startsWith('prayerNotification_')) {
        chrome.alarms.clear(alarm.name);
      }
    });

    // If somehow the fetch alarm was lost, recreate it
    if (!fetchAlarm) {
      setupDailyAlarm();
    }

    // Schedule the prayer notifications
    scheduleNotifications(timings, settings);
  });
}

// Helper function to schedule the actual notification alarms
function scheduleNotifications(timings, settings) {
  // Current time
  const now = new Date();

  // Schedule notifications for each prayer
  Object.entries(timings).forEach(([prayer, timeStr]) => {
    // Skip if this prayer's notifications are disabled
    if (!settings.prayers[prayer]) return;

    // Skip if timeStr is not provided
    if (!timeStr) return;

    // Parse the prayer time (format: "HH:MM" or "HH:MM:SS")
    let prayerTime;
    if (timeStr.includes('T')) {
      // Handle ISO format (2023-05-20T04:30:00)
      prayerTime = new Date(timeStr);
    } else {
      // Handle HH:MM or HH:MM:SS format
      const timeParts = timeStr.split(':');
      const hours = parseInt(timeParts[0], 10);
      const minutes = parseInt(timeParts[1], 10);

      prayerTime = new Date();
      prayerTime.setHours(hours, minutes, 0, 0);
    }

    // Only schedule if the prayer time is in the future
    if (prayerTime > now) {
      const minutesUntilPrayer = (prayerTime - now) / (60 * 1000);

      // Create an alarm for this prayer notification
      chrome.alarms.create(`prayerNotification_${prayer}`, {
        delayInMinutes: minutesUntilPrayer
      });

      console.log(`Notification for ${prayer} scheduled in ${minutesUntilPrayer.toFixed(2)} minutes`);
    }
  });
}

// Send a prayer time notification
function sendPrayerNotification(prayer) {
  chrome.storage.sync.get(['city', 'notificationSettings'], (result) => {
    if (!result.city || !result.notificationSettings || !result.notificationSettings.enabled) {
      return;
    }

    const cityName = result.city.name;
    const countryName = result.city.country;

    // Map prayer names to their English translations
    const prayerNames = {
      'Fajr': 'Dawn',
      'Dhuhr': 'Noon',
      'Asr': 'Afternoon',
      'Maghrib': 'Dusk',
      'Isha': 'Dinner',
      'Sunrise': 'Sunrise'
    };

    const displayName = prayerNames[prayer] || prayer;

    chrome.notifications.create(`prayer_${prayer}_${Date.now()}`, {
      type: 'basic',
      iconUrl: 'android-chrome-192x192.png',
      title: `${displayName} Prayer Time`,
      message: `It is time for the ${displayName} prayer in ${cityName}, ${countryName}.`,
      priority: 2
    });
  });
}

// Listen for changes in storage
chrome.storage.onChanged.addListener((changes, namespace) => {
  if (namespace === 'sync' && (changes.city || changes.notificationSettings)) {
    console.log('City or notification settings changed, updating prayer times');
    fetchPrayerTimesAndScheduleNotifications();
  }
});

// Initialize extension
function initializeExtension() {
  console.log('Tavoos extension initialized');
  fetchPrayerTimesAndScheduleNotifications();
}

initializeExtension();
